defmodule ExampleSetup do
  @moduledoc """
  Helper functions for setting up database state in examples.

  This module provides utilities for examples to set up database tables
  and seed data as needed.
  """

  @doc """
  Sets up the database with the necessary tables for the given schemas.
  Call this at the beginning of your example if you need database access.
  """
  def setup_database(schemas \\ []) do
    repo_config = Application.get_env(:drops, Drops.Repo.Sqlite, [])

    pid =
      if repo_config[:pool] == Ecto.Adapters.SQL.Sandbox do
        Ecto.Adapters.SQL.Sandbox.start_owner!(Drops.Repo.Sqlite, shared: false)
      else
        :no_sandbox
      end

    pid
  end

  def cleanup_database(:no_sandbox), do: :ok

  def cleanup_database(pid) do
    Ecto.Adapters.SQL.Sandbox.stop_owner(pid)
  end
end
